
import { motion } from 'framer-motion';
import { Briefcase, Globe, ShoppingCart, Calendar, MapPin } from 'lucide-react';

const InternshipSection = () => {
  const internships = [
    {
      title: "Web Development Internship",
      company: "CAUSEVE TECHNOLOGIES LLP",
      duration: "Completed",
      description: "Gained hands-on experience in building dynamic websites using HTML, CSS, JavaScript, PHP, and MySQL. Learned to develop responsive user interfaces, manage backend operations, and implement CRUD functionalities for real-time web applications.",
      icon: Globe,
      color: "blue",
      tech: ["HTML", "CSS", "JavaScript", "PHP", "MySQL"],
      type: "Full Stack Development"
    },
    {
      title: "Amazon Price Alert System",
      company: "CYBERNAUT EDUTECH PVT LTD",
      duration: "Completed",
      description: "Developed an automated Amazon price alert system that monitors selected product prices and automatically sends email alerts when prices drop below a user-defined threshold. The system uses web scraping and email automation for continuous monitoring.",
      icon: ShoppingCart,
      color: "orange",
      tech: ["Python", "BeautifulSoup", "Requests", "smtplib"],
      type: "Automation & Web Scraping",
      website: "https://cybernaut.co.in/"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-400 bg-blue-500/20",
      orange: "from-orange-500/20 to-orange-600/20 border-orange-500/30 text-orange-400 bg-orange-500/20"
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 px-4" id="internships">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
              Internship Experience
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto rounded-full" />
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8">
          {internships.map((internship, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className={`group relative p-6 bg-gradient-to-br ${getColorClasses(internship.color)} backdrop-blur-sm border rounded-2xl hover:shadow-2xl transition-all duration-500`}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className={`p-3 ${getColorClasses(internship.color)} rounded-full group-hover:scale-110 transition-transform duration-300`}>
                    <internship.icon className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white group-hover:text-current transition-colors">
                      {internship.title}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Briefcase className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-400">{internship.company}</span>
                    </div>
                  </div>
                </div>
                <span className="px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full text-xs text-white">
                  {internship.type}
                </span>
              </div>

              {/* Company info */}
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-400">{internship.duration}</span>
                </div>
                {internship.website && (
                  <a
                    href={internship.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-current hover:underline"
                  >
                    Visit Website
                  </a>
                )}
              </div>

              {/* Description */}
              <p className="text-gray-300 text-sm leading-relaxed mb-6">
                {internship.description}
              </p>

              {/* Tech Stack */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-white">Technologies Used:</h4>
                <div className="flex flex-wrap gap-2">
                  {internship.tech.map((tech, techIndex) => (
                    <motion.span
                      key={techIndex}
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ delay: techIndex * 0.1 }}
                      viewport={{ once: true }}
                      className="px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full text-xs text-gray-300 border border-white/20"
                    >
                      {tech}
                    </motion.span>
                  ))}
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-white/10 to-transparent rounded-tr-2xl" />
              <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-white/5 to-transparent rounded-bl-2xl" />

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default InternshipSection;
