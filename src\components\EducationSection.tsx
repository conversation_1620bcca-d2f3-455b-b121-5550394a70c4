
import { motion } from 'framer-motion';
import { GraduationCap, Calendar, MapPin } from 'lucide-react';

const EducationSection = () => {
  const education = [
    {
      degree: "B.Tech Artificial Intelligence & Data Science",
      institution: "Mahendra Engineering College",
      year: "2021-2025",
      type: "Bachelor's Degree",
      color: "purple"
    },
    {
      degree: "Higher Secondary Certificate (HSC)",
      institution: "Sathya Saai Matric Higher Secondary School",
      year: "2020-2021",
      type: "Higher Secondary",
      color: "blue"
    },
    {
      degree: "Secondary School Leaving Certificate (SSLC)",
      institution: "Sathya Saai Matric Higher Secondary School",
      year: "2018-2019",
      type: "Secondary",
      color: "teal"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      purple: "from-purple-500/20 to-purple-600/20 border-purple-500/30 bg-purple-500/20",
      blue: "from-blue-500/20 to-blue-600/20 border-blue-500/30 bg-blue-500/20",
      teal: "from-teal-500/20 to-teal-600/20 border-teal-500/30 bg-teal-500/20"
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 px-4" id="education">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-teal-400 to-green-400 bg-clip-text text-transparent">
              Education
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal-500 to-green-500 mx-auto rounded-full" />
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 via-blue-500 to-teal-500 transform md:-translate-x-1/2" />

          <div className="space-y-12">
            {education.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={`relative flex items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                } flex-col md:justify-between`}
              >
                {/* Timeline dot */}
                <div className="absolute left-8 md:left-1/2 w-4 h-4 bg-white rounded-full transform md:-translate-x-1/2 border-4 border-gray-900 z-10">
                  <div className={`w-full h-full rounded-full ${getColorClasses(edu.color)}`} />
                </div>

                {/* Content card */}
                <div className={`w-full md:w-5/12 ml-16 md:ml-0 ${
                  index % 2 === 0 ? 'md:mr-8' : 'md:ml-8'
                }`}>
                  <motion.div
                    whileHover={{ scale: 1.05, y: -5 }}
                    className={`p-6 bg-gradient-to-br ${getColorClasses(edu.color)} backdrop-blur-sm border rounded-2xl`}
                  >
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`p-2 ${getColorClasses(edu.color)} rounded-full`}>
                        <GraduationCap className="w-5 h-5 text-white" />
                      </div>
                      <span className="px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full text-xs text-white">
                        {edu.type}
                      </span>
                    </div>

                    <h3 className="text-xl font-bold text-white mb-2">
                      {edu.degree}
                    </h3>

                    <div className="space-y-2 text-gray-300">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        <span className="text-sm">{edu.institution}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span className="text-sm">{edu.year}</span>
                      </div>
                    </div>

                    {/* Decorative gradient */}
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-white/10 to-transparent rounded-tl-2xl rounded-br-2xl" />
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default EducationSection;
