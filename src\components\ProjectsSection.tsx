
import { motion } from 'framer-motion';
import { <PERSON>, Leaf, Hand, ExternalLink, Github } from 'lucide-react';

const ProjectsSection = () => {
  const projects = [
    {
      title: "Skin Disease Prediction Using Machine Learning",
      description: "Developed a machine learning model using convolutional neural networks (CNNs) to predict skin diseases from images. Achieved over 85% accuracy in classifying conditions such as melanoma and eczema, demonstrating the potential for AI to assist in early diagnosis.",
      icon: Brain,
      color: "purple",
      tech: ["Python", "CNN", "TensorFlow", "Image Processing"],
      accuracy: "85%+"
    },
    {
      title: "Crop Condition Identification Using ML",
      description: "This project uses machine learning and VGG-16-based image analysis to detect crop issues early, such as diseases or nutrient deficiencies in corn and tapioca. It enables faster, accurate, and scalable monitoring, reducing yield loss and promoting smart, data-driven farming.",
      icon: Leaf,
      color: "green",
      tech: ["VGG-16", "Computer Vision", "Agriculture AI", "Python"],
      accuracy: "90%+"
    },
    {
      title: "AI Based Gesture Media Controller Using IoT",
      description: "This project enhances human-computer interaction by using real-time hand gesture recognition to control a media player. It supports accessibility, especially for the mute and dumb, enabling seamless communication without touch or voice.",
      icon: Hand,
      color: "blue",
      tech: ["Computer Vision", "IoT", "Real-time Processing", "OpenCV"],
      accuracy: "Real-time"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      purple: "from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-400 bg-purple-500/20",
      green: "from-green-500/20 to-green-600/20 border-green-500/30 text-green-400 bg-green-500/20",
      blue: "from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-400 bg-blue-500/20"
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 px-4" id="projects">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              College Projects
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-blue-500 mx-auto rounded-full" />
        </motion.div>

        <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -10 }}
              className={`group relative p-6 bg-gradient-to-br ${getColorClasses(project.color)} backdrop-blur-sm border rounded-2xl hover:shadow-2xl transition-all duration-500`}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div className={`p-3 ${getColorClasses(project.color)} rounded-full group-hover:scale-110 transition-transform duration-300`}>
                  <project.icon className="w-6 h-6" />
                </div>
                <div className="flex gap-2">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
                  >
                    <Github className="w-4 h-4 text-white" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
                  >
                    <ExternalLink className="w-4 h-4 text-white" />
                  </motion.button>
                </div>
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-white mb-4 group-hover:text-current transition-colors">
                {project.title}
              </h3>

              <p className="text-gray-300 text-sm leading-relaxed mb-6">
                {project.description}
              </p>

              {/* Accuracy Badge */}
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full mb-4">
                <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                <span className="text-sm font-medium text-white">{project.accuracy}</span>
              </div>

              {/* Tech Stack */}
              <div className="space-y-2">
                <h4 className="text-sm font-semibold text-white">Tech Stack:</h4>
                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 bg-white/10 backdrop-blur-sm rounded text-xs text-gray-300"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-white/10 to-transparent rounded-tr-2xl" />
              <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-white/5 to-transparent rounded-bl-2xl" />

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
