
import { motion } from 'framer-motion';
import { Ch<PERSON>ronD<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Brain } from 'lucide-react';

const HeroSection = () => {
  return (
    <section className="min-h-screen flex items-center justify-center relative">
      <div className="text-center z-10 px-4">
        {/* Floating icons */}
        <motion.div
          className="absolute top-20 left-20"
          animate={{ y: [-10, 10, -10] }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          <Brain className="w-8 h-8 text-purple-400/50" />
        </motion.div>
        <motion.div
          className="absolute top-32 right-16"
          animate={{ y: [10, -10, 10] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          <Code className="w-6 h-6 text-blue-400/50" />
        </motion.div>
        <motion.div
          className="absolute bottom-40 left-16"
          animate={{ y: [-15, 15, -15] }}
          transition={{ duration: 5, repeat: Infinity }}
        >
          <Sparkles className="w-7 h-7 text-teal-400/50" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="space-y-6"
        >
          <motion.h1 
            className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-purple-400 via-blue-400 to-teal-400 bg-clip-text text-transparent"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 1.2, ease: "easeOut" }}
          >
            SANJAI S
          </motion.h1>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 1 }}
            className="relative"
          >
            <h2 className="text-2xl md:text-3xl text-gray-300 font-light">
              Junior Software Developer
            </h2>
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/20 to-teal-500/20 blur-lg rounded-lg" />
          </motion.div>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
            className="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed"
          >
            AI & Data Science Graduate | Passionate about creating intelligent systems 
            and modern software solutions that push the boundaries of technology
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5, duration: 1 }}
            className="flex flex-wrap justify-center gap-4 mt-8"
          >
            <span className="px-4 py-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full text-purple-300 text-sm">
              Machine Learning
            </span>
            <span className="px-4 py-2 bg-blue-500/20 backdrop-blur-sm border border-blue-500/30 rounded-full text-blue-300 text-sm">
              Full Stack Development
            </span>
            <span className="px-4 py-2 bg-teal-500/20 backdrop-blur-sm border border-teal-500/30 rounded-full text-teal-300 text-sm">
              AI Innovation
            </span>
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="flex flex-col items-center"
          >
            <ChevronDown className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-500 mt-2">Scroll to explore</span>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
