
import { motion } from 'framer-motion';
import { User, Target, Lightbulb } from 'lucide-react';

const ProfileSection = () => {
  return (
    <section className="py-20 px-4" id="profile">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              Profile Summary
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto rounded-full" />
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-purple-500/20 rounded-full">
                  <User className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold text-purple-300">About Me</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Motivated and diligent Artificial Intelligence graduate seeking to apply academic knowledge 
                and skills in a professional work environment. I consider myself a responsible and orderly person 
                looking forward to my first work experience.
              </p>
            </div>

            <div className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-blue-500/20 rounded-full">
                  <Target className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-blue-300">Objective</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Eager to contribute to innovative projects and gain practical experience in the field of AI. 
                Looking to leverage my academic foundation in machine learning and software development 
                to create impactful solutions.
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="aspect-square rounded-full bg-gradient-to-br from-purple-500/20 via-blue-500/20 to-teal-500/20 p-1">
              <div className="w-full h-full rounded-full bg-gray-900/80 backdrop-blur-sm flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="relative">
                    <div className="w-32 h-32 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full mx-auto flex items-center justify-center">
                      <span className="text-4xl font-bold text-white">S</span>
                    </div>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="absolute inset-0 border-2 border-dashed border-teal-400/30 rounded-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-white">AI Enthusiast</h3>
                    <p className="text-sm text-gray-400">2025 Graduate</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating elements around the circle */}
            <motion.div
              animate={{ y: [-10, 10, -10], rotate: [0, 180, 360] }}
              transition={{ duration: 6, repeat: Infinity }}
              className="absolute top-4 right-4 p-2 bg-purple-500/20 rounded-full"
            >
              <Lightbulb className="w-4 h-4 text-purple-400" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ProfileSection;
