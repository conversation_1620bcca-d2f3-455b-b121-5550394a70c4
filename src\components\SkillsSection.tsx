
import { motion } from 'framer-motion';
import { Code, Database, Globe, Cpu, Brain, Zap } from 'lucide-react';

const SkillsSection = () => {
  const skillCategories = [
    {
      title: "Programming Languages",
      icon: Code,
      color: "purple",
      skills: ["Java", "Python", "C++", "JavaScript"]
    },
    {
      title: "Frameworks & Libraries",
      icon: Zap,
      color: "blue",
      skills: ["Angular", "React", "Bootstrap", "Tailwind CSS"]
    },
    {
      title: "Web Technologies",
      icon: Globe,
      color: "teal",
      skills: ["HTML5", "CSS3", "SCSS", "JavaScript"]
    },
    {
      title: "Databases",
      icon: Database,
      color: "green",
      skills: ["SQL", "MongoDB"]
    },
    {
      title: "AI & Machine Learning",
      icon: Brain,
      color: "pink",
      skills: ["CNN", "Computer Vision", "Deep Learning", "Image Processing"]
    },
    {
      title: "Development Tools",
      icon: Cpu,
      color: "orange",
      skills: ["Git", "VSCode", "PyCharm", "<PERSON><PERSON><PERSON>"]
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      purple: "from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-400",
      blue: "from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-400",
      teal: "from-teal-500/20 to-teal-600/20 border-teal-500/30 text-teal-400",
      green: "from-green-500/20 to-green-600/20 border-green-500/30 text-green-400",
      pink: "from-pink-500/20 to-pink-600/20 border-pink-500/30 text-pink-400",
      orange: "from-orange-500/20 to-orange-600/20 border-orange-500/30 text-orange-400"
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 px-4" id="skills">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">
              Technical Skills
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-teal-500 mx-auto rounded-full" />
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skillCategories.map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -10 }}
              className={`p-6 bg-gradient-to-br ${getColorClasses(category.color)} backdrop-blur-sm border rounded-2xl hover:shadow-2xl transition-all duration-300`}
            >
              <div className="flex items-center gap-4 mb-6">
                <div className={`p-3 bg-gradient-to-br ${getColorClasses(category.color)} rounded-full`}>
                  <category.icon className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-white">{category.title}</h3>
              </div>
              
              <div className="space-y-3">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: (index * 0.1) + (skillIndex * 0.05) }}
                    viewport={{ once: true }}
                    className="flex items-center gap-3"
                  >
                    <div className="w-2 h-2 bg-current rounded-full" />
                    <span className="text-gray-300">{skill}</span>
                  </motion.div>
                ))}
              </div>

              {/* Decorative elements */}
              <div className="absolute top-2 right-2 w-16 h-16 bg-gradient-to-br from-white/5 to-transparent rounded-full" />
              <div className="absolute bottom-2 left-2 w-8 h-8 bg-gradient-to-br from-white/10 to-transparent rounded-full" />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;
