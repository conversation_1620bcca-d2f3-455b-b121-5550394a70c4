
import { motion } from 'framer-motion';
import { Award, CheckCircle } from 'lucide-react';

const CertificationsSection = () => {
  const certifications = [
    {
      title: "Java Course Completion Certificate",
      language: "Java",
      color: "red",
      level: "Fundamental"
    },
    {
      title: "Python Course Completion Certificate",
      language: "Python",
      color: "blue",
      level: "Advanced"
    },
    {
      title: "C++ Course Completion Certificate",
      language: "C++",
      color: "purple",
      level: "Intermediate"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      red: "from-red-500/20 to-red-600/20 border-red-500/30 text-red-400 bg-red-500/20",
      blue: "from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-400 bg-blue-500/20",
      purple: "from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-400 bg-purple-500/20"
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section className="py-20 px-4" id="certifications">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
              Certifications
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-yellow-500 to-orange-500 mx-auto rounded-full" />
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {certifications.map((cert, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50, rotateY: -15 }}
              whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, rotateY: 5 }}
              className={`group relative p-6 bg-gradient-to-br ${getColorClasses(cert.color)} backdrop-blur-sm border rounded-2xl hover:shadow-2xl transition-all duration-500 transform-gpu`}
            >
              {/* Certificate Icon */}
              <div className="flex justify-center mb-6">
                <div className={`relative p-4 ${getColorClasses(cert.color)} rounded-full group-hover:scale-110 transition-transform duration-300`}>
                  <Award className="w-8 h-8" />
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0 border-2 border-dashed border-current rounded-full opacity-30"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="text-center space-y-4">
                <h3 className="text-lg font-bold text-white group-hover:text-current transition-colors">
                  {cert.title}
                </h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-2xl font-bold text-current">{cert.language}</span>
                  </div>
                  
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-gray-300">{cert.level} Level</span>
                  </div>
                </div>

                <div className="pt-4">
                  <span className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm text-white">
                    <Award className="w-4 h-4" />
                    Certified
                  </span>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-2 right-2 w-12 h-12 bg-gradient-to-bl from-white/10 to-transparent rounded-full" />
              <div className="absolute bottom-2 left-2 w-8 h-8 bg-gradient-to-tr from-white/5 to-transparent rounded-full" />

              {/* Shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-500" />
            </motion.div>
          ))}
        </div>

        {/* Achievement Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-12 text-center"
        >
          <div className="inline-flex items-center gap-4 px-6 py-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm border border-yellow-500/30 rounded-full">
            <Award className="w-5 h-5 text-yellow-400" />
            <span className="text-white font-medium">3 Programming Certifications Completed</span>
            <CheckCircle className="w-5 h-5 text-green-400" />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CertificationsSection;
